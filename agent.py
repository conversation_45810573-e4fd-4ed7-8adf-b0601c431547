import json
import os
import time
import subprocess
import threading
import asyncio
import concurrent.futures
import re
import ast
import shutil
import glob
import urllib.request
import urllib.parse
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import hashlib
import pickle

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage, SystemMessage, AIMessage
from langchain.tools import Tool
from langchain.agents import AgentExecutor, create_react_agent
from langchain.prompts import PromptTemplate
from langchain.memory import ConversationBufferWindowMemory
from langchain.callbacks import StreamingStdOutCallbackHandler

from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Gemini AI
llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    google_api_key=os.getenv("GEMINI_API_KEY"),
    temperature=0.1,
    streaming=True,
    callbacks=[StreamingStdOutCallbackHandler()]
)

# Context and State Management
@dataclass
class AgentContext:
    current_directory: str = os.getcwd()
    active_files: List[str] = None
    command_history: List[str] = None
    project_structure: Dict = None
    last_error: str = ""
    working_memory: Dict = None

    def __post_init__(self):
        if self.active_files is None:
            self.active_files = []
        if self.command_history is None:
            self.command_history = []
        if self.working_memory is None:
            self.working_memory = {}

class AdvancedCodingAgent:
    def __init__(self):
        self.context = AgentContext()
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        self.memory = ConversationBufferWindowMemory(k=10, return_messages=True)
        self.cache = {}
        self.running_processes = {}

    def get_system_info(self) -> str:
        """Get comprehensive system information"""
        try:
            import platform
            info = {
                "os": platform.system(),
                "version": platform.version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "current_dir": os.getcwd(),
                "user": os.getenv("USERNAME") or os.getenv("USER", "unknown")
            }
            return json.dumps(info, indent=2)
        except Exception as e:
            return f"Error getting system info: {str(e)}"

    def run_command(self, command: str, timeout: int = 30) -> str:
        """Execute PowerShell commands with advanced error handling"""
        try:
            self.context.command_history.append(command)

            # Use PowerShell for Windows
            if os.name == 'nt':
                cmd = ['powershell', '-Command', command]
            else:
                cmd = command

            result = subprocess.run(
                cmd,
                shell=True if os.name != 'nt' else False,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=self.context.current_directory
            )

            output = result.stdout.strip() if result.stdout else ""
            error = result.stderr.strip() if result.stderr else ""

            if result.returncode == 0:
                return f"✅ Command executed successfully:\n{output}"
            else:
                self.context.last_error = error
                return f"❌ Command failed (code {result.returncode}):\n{error}\nOutput: {output}"

        except subprocess.TimeoutExpired:
            return f"⏰ Command timed out after {timeout} seconds"
        except Exception as e:
            error_msg = f"❌ Error executing command: {str(e)}"
            self.context.last_error = error_msg
            return error_msg
    def write_file(self, path: str, content: str, backup: bool = True) -> str:
        """Advanced file writing with backup and validation"""
        try:
            abs_path = os.path.abspath(path)
            dir_path = os.path.dirname(abs_path)

            # Create backup if file exists
            if backup and os.path.exists(abs_path):
                backup_path = f"{abs_path}.backup_{int(time.time())}"
                shutil.copy2(abs_path, backup_path)

            # Ensure directory exists
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)

            # Write file with encoding
            with open(abs_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Update context
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)

            # Validate written content
            with open(abs_path, 'r', encoding='utf-8') as f:
                written_content = f.read()

            if written_content == content:
                return f"✅ File '{path}' written successfully ({len(content)} chars)"
            else:
                return f"⚠️ File written but content validation failed"

        except Exception as e:
            return f"❌ Error writing file '{path}': {str(e)}"


    def read_file(self, file_path: str, lines: Optional[Tuple[int, int]] = None) -> str:
        """Advanced file reading with line range support"""
        try:
            abs_path = os.path.abspath(file_path)

            if not os.path.exists(abs_path):
                return f"❌ File not found: {file_path}"

            with open(abs_path, 'r', encoding='utf-8') as f:
                if lines:
                    all_lines = f.readlines()
                    start, end = lines
                    selected_lines = all_lines[start-1:end] if end != -1 else all_lines[start-1:]
                    content = ''.join(selected_lines)
                else:
                    content = f.read()

            # Update context
            if abs_path not in self.context.active_files:
                self.context.active_files.append(abs_path)

            return f"✅ File content ({len(content)} chars):\n{content}"

        except Exception as e:
            return f"❌ Error reading file '{file_path}': {str(e)}"
    

    def search_files(self, pattern: str, directory: str = ".", file_types: List[str] = None) -> str:
        """Search for files and content with advanced filtering"""
        try:
            if file_types is None:
                file_types = ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.json", "*.md", "*.txt", "*.yml", "*.yaml", "*.sh", "*.bash", "*.ps1", "*.cmd", "*.bat", "*.ini", "*.cfg", "*.conf", "*.xml", "*.csv", "*.log"]

            results = []
            search_dir = os.path.abspath(directory)

            for file_type in file_types:    
                for file_path in glob.glob(os.path.join(search_dir, "**", file_type), recursive=True):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if re.search(pattern, content, re.IGNORECASE):
                                # Find matching lines
                                lines = content.split('\n')
                                matches = []
                                for i, line in enumerate(lines, 1):
                                    if re.search(pattern, line, re.IGNORECASE):
                                        matches.append(f"  Line {i}: {line.strip()}")

                                results.append(f"📁 {file_path}:\n" + "\n".join(matches[:5]))
                    except:
                        continue

            if results:
                return f"🔍 Found {len(results)} files matching '{pattern}':\n\n" + "\n\n".join(results[:10])
            else:
                return f"❌ No files found matching pattern '{pattern}'"

        except Exception as e:
            return f"❌ Error searching files: {str(e)}"

    def get_web_info(self, query: str) -> str:
        """Retrieve information from web without search engine API"""
        try:
            # Simple web scraping for documentation and info
            encoded_query = urllib.parse.quote(query)
            urls = [
                f"https://docs.python.org/3/search.html?q={encoded_query}",
                f"https://developer.mozilla.org/en-US/search?q={encoded_query}",
                f"https://stackoverflow.com/search?q={encoded_query}"
            ]

            results = []
            for url in urls[:2]:  # Limit to avoid rate limiting
                try:
                    with urllib.request.urlopen(url, timeout=10) as response:
                        content = response.read().decode('utf-8')
                        # Extract useful text (simplified)
                        text_content = re.sub(r'<[^>]+>', '', content)
                        text_content = re.sub(r'\s+', ' ', text_content)
                        results.append(text_content[:500] + "...")
                except:
                    continue

            if results:
                return f"🌐 Web information for '{query}':\n\n" + "\n\n".join(results)
            else:
                return f"❌ Could not retrieve web information for '{query}'"

        except Exception as e:
            return f"❌ Error retrieving web info: {str(e)}"

    def analyze_code(self, code: str, language: str = "python") -> str:
        """Advanced code analysis with AST parsing"""
        try:
            if language.lower() == "python":
                try:
                    tree = ast.parse(code)
                    analysis = {
                        "functions": [],
                        "classes": [],
                        "imports": [],
                        "variables": [],
                        "complexity": 0
                    }

                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            analysis["functions"].append(node.name)
                        elif isinstance(node, ast.ClassDef):
                            analysis["classes"].append(node.name)
                        elif isinstance(node, ast.Import):
                            for alias in node.names:
                                analysis["imports"].append(alias.name)
                        elif isinstance(node, ast.ImportFrom):
                            analysis["imports"].append(f"from {node.module}")
                        elif isinstance(node, ast.Assign):
                            for target in node.targets:
                                if isinstance(target, ast.Name):
                                    analysis["variables"].append(target.id)

                    return f"📊 Code Analysis:\n{json.dumps(analysis, indent=2)}"
                except SyntaxError as e:
                    return f"❌ Syntax Error in code: {str(e)}"
            else:
                return f"🔍 Basic analysis for {language} code:\nLines: {len(code.split())}\nCharacters: {len(code)}"

        except Exception as e:
            return f"❌ Error analyzing code: {str(e)}"

    def fix_errors(self, error_log: str, code_context: str = "") -> str:
        """Advanced error analysis and fixing suggestions"""
        try:
            suggestions = []
            fixes = []

            # Common error patterns and fixes
            error_patterns = {
                r"ModuleNotFoundError.*'(\w+)'": lambda m: f"pip install {m.group(1)}",
                r"SyntaxError.*line (\d+)": lambda m: f"Check syntax on line {m.group(1)}",
                r"IndentationError": lambda m: "Fix indentation - use consistent spaces/tabs",
                r"NameError.*'(\w+)'": lambda m: f"Variable '{m.group(1)}' not defined - check spelling",
                r"FileNotFoundError.*'([^']+)'": lambda m: f"File '{m.group(1)}' not found - check path",
                r"port.*already in use": lambda m: "Change port number or kill existing process",
                r"Permission denied": lambda m: "Run with administrator privileges or check file permissions"
            }

            for pattern, fix_func in error_patterns.items():
                matches = re.finditer(pattern, error_log, re.IGNORECASE)
                for match in matches:
                    fix = fix_func(match)
                    if fix not in fixes:
                        fixes.append(fix)

            # AI-powered suggestions based on context
            if code_context:
                if "import" in error_log.lower() and "module" in error_log.lower():
                    missing_modules = re.findall(r"No module named '(\w+)'", error_log)
                    for module in missing_modules:
                        fixes.append(f"Install missing module: pip install {module}")

            if fixes:
                return f"🔧 Error Analysis & Fixes:\n" + "\n".join([f"• {fix}" for fix in fixes])
            else:
                return f"🤔 Complex error detected. Manual review needed:\n{error_log[:500]}"

        except Exception as e:
            return f"❌ Error analyzing error log: {str(e)}"

    def generate_code(self, description: str, language: str = "python") -> str:
        """AI-powered code generation"""
        try:
            prompt = f"""Generate {language} code for: {description}

Requirements:
- Write clean, production-ready code
- Include proper error handling
- Add comments for complex logic
- Follow best practices for {language}
- Make it modular and reusable

Code:"""

            # Use LangChain to generate code
            response = llm.invoke([HumanMessage(content=prompt)])
            generated_code = response.content

            # Extract code from response
            code_match = re.search(r'```(?:' + language + r')?\n(.*?)\n```', generated_code, re.DOTALL)
            if code_match:
                return f"🤖 Generated {language} code:\n```{language}\n{code_match.group(1)}\n```"
            else:
                return f"🤖 Generated {language} code:\n{generated_code}"

        except Exception as e:
            return f"❌ Error generating code: {str(e)}"

    def refactor_code(self, code: str, refactor_type: str = "optimize") -> str:
        """AI-powered code refactoring"""
        try:
            refactor_prompts = {
                "optimize": "Optimize this code for better performance and readability",
                "modularize": "Break this code into smaller, reusable functions/modules",
                "clean": "Clean up this code - remove duplicates, improve naming, add comments",
                "secure": "Make this code more secure - fix potential vulnerabilities"
            }

            prompt = f"""{refactor_prompts.get(refactor_type, refactor_prompts['optimize'])}:

Original Code:
```
{code}
```

Refactored Code:"""

            response = llm.invoke([HumanMessage(content=prompt)])
            return f"🔄 Refactored code ({refactor_type}):\n{response.content}"

        except Exception as e:
            return f"❌ Error refactoring code: {str(e)}"

    def get_project_structure(self, directory: str = ".") -> str:
        """Get comprehensive project structure"""
        try:
            structure = {}

            def build_tree(path, max_depth=3, current_depth=0):
                if current_depth >= max_depth:
                    return "..."

                items = {}
                try:
                    for item in sorted(os.listdir(path)):
                        if item.startswith('.'):
                            continue
                        item_path = os.path.join(path, item)
                        if os.path.isdir(item_path):
                            items[f"📁 {item}/"] = build_tree(item_path, max_depth, current_depth + 1)
                        else:
                            size = os.path.getsize(item_path)
                            items[f"📄 {item}"] = f"{size} bytes"
                except PermissionError:
                    items["❌ Permission Denied"] = ""
                return items

            structure = build_tree(os.path.abspath(directory))
            self.context.project_structure = structure

            return f"📂 Project Structure:\n{json.dumps(structure, indent=2)}"

        except Exception as e:
            return f"❌ Error getting project structure: {str(e)}"


    def run_tests(self, test_path: str = ".", test_type: str = "auto") -> str:
        """Run tests with auto-detection"""
        try:
            test_commands = {
                "python": ["python -m pytest", "python -m unittest discover"],
                "javascript": ["npm test", "yarn test", "jest"],
                "node": ["npm test", "mocha"],
                "auto": []
            }

            if test_type == "auto":
                # Auto-detect test framework
                if os.path.exists("package.json"):
                    test_commands["auto"] = test_commands["javascript"]
                elif any(f.endswith(".py") for f in os.listdir(".")):
                    test_commands["auto"] = test_commands["python"]
                else:
                    return "❌ Could not auto-detect test framework"

            commands = test_commands.get(test_type, test_commands["auto"])

            for cmd in commands:
                result = self.run_command(cmd)
                if "✅" in result:
                    return f"🧪 Tests executed:\n{result}"

            return "❌ No suitable test command found"

        except Exception as e:
            return f"❌ Error running tests: {str(e)}"

    def create_tools(self):
        """Create LangChain tools from agent methods"""
        return [
            Tool(
                name="run_command",
                description="Execute PowerShell/terminal commands with advanced error handling",
                func=lambda cmd: self.run_command(cmd)
            ),
            Tool(
                name="write_file",
                description="Write files with backup and validation. Format: path|content",
                func=lambda args: self.write_file(args.split("|")[0], "|".join(args.split("|")[1:]))
            ),
            Tool(
                name="read_file",
                description="Read files with line range support",
                func=lambda path: self.read_file(path)
            ),
            Tool(
                name="search_files",
                description="Search for files and content with pattern matching",
                func=lambda pattern: self.search_files(pattern)
            ),
            Tool(
                name="get_web_info",
                description="Retrieve information from web sources",
                func=lambda query: self.get_web_info(query)
            ),
            Tool(
                name="analyze_code",
                description="Analyze code structure and complexity",
                func=lambda code: self.analyze_code(code)
            ),
            Tool(
                name="fix_errors",
                description="Advanced error analysis and fixing suggestions",
                func=lambda error: self.fix_errors(error)
            ),
            Tool(
                name="generate_code",
                description="AI-powered code generation",
                func=lambda desc: self.generate_code(desc)
            ),
            Tool(
                name="refactor_code",
                description="AI-powered code refactoring",
                func=lambda code: self.refactor_code(code)
            ),
            Tool(
                name="get_project_structure",
                description="Get comprehensive project structure",
                func=lambda dir: self.get_project_structure(dir if dir else ".")
            ),
            Tool(
                name="get_system_info",
                description="Get comprehensive system information",
                func=lambda: self.get_system_info()
            ),
            Tool(
                name="run_tests",
                description="Run tests with auto-detection",
                func=lambda path: self.run_tests(path if path else ".")
            )
        ]

    def create_agent_prompt(self):
        """Create comprehensive system prompt for the agent"""
        return """You are an ADVANCED AUTONOMOUS CLI CODING AGENT powered by Gemini AI.

🎯 CORE CAPABILITIES:
- Full-stack development (Python, JavaScript, TypeScript, React, Node.js, etc.)
- Advanced file operations and project management
- Terminal command execution with PowerShell support
- Code analysis, generation, and refactoring
- Error detection and autonomous fixing
- Web information retrieval
- Multi-threaded task execution
- Context-aware decision making

🧠 INTELLIGENCE FEATURES:
- Natural language processing for user commands
- Predictive prefetching of likely next actions
- Chain-of-thought reasoning for complex problems
- Self-critique and optimization
- Context compression and smart suggestions
- Autonomous debugging and error resolution

🔄 WORKFLOW PROCESS:
1. ANALYZE: Understand user input and current context
2. PLAN: Create step-by-step execution plan
3. EXECUTE: Perform one action at a time
4. OBSERVE: Analyze results and learn from output
5. ADAPT: Adjust plan based on observations
6. CONTINUE: Iterate until goal achieved

🛠️ AVAILABLE TOOLS:
- run_command: Execute terminal/PowerShell commands
- write_file: Create/modify files with backup
- read_file: Read files with line range support
- search_files: Search content across project files
- get_web_info: Retrieve web information
- analyze_code: AST-based code analysis
- fix_errors: Advanced error diagnosis and fixes
- generate_code: AI-powered code generation
- refactor_code: Intelligent code refactoring
- get_project_structure: Project tree analysis
- get_system_info: System environment details
- run_tests: Auto-detect and run tests

🎯 BEHAVIOR RULES:
- Execute ONE step at a time, wait for observation
- Always analyze results before next action
- Create project folders for new applications
- Use PowerShell commands on Windows
- Handle errors gracefully with auto-fix attempts
- Maintain context awareness throughout session
- Provide clear status updates and progress tracking
- Never execute dangerous commands
- Follow coding best practices and security guidelines

🚀 AUTONOMOUS FEATURES:
- Auto-detect project type and requirements
- Intelligent dependency management
- Background error monitoring and fixing
- Predictive code suggestions
- Context-aware refactoring recommendations
- Smart project structure organization

RESPOND WITH NATURAL LANGUAGE - NO JSON FORMAT REQUIRED.
Be conversational, helpful, and demonstrate your advanced capabilities.
Always explain what you're doing and why."""

    def run_agent(self):
        """Main agent execution loop"""
        print("🤖 Advanced CLI Coding Agent v2.0 - Powered by Gemini AI")
        print("=" * 60)
        print("🎯 I can help you build anything - just describe what you need!")
        print("💡 Examples: 'Create a React app', 'Fix this Python error', 'Analyze my code'")
        print("🔧 Type 'help' for capabilities, 'exit' to quit")
        print("=" * 60)

        # Create agent with tools
        tools = self.create_tools()

        # Create prompt template for ReAct agent
        prompt_template = """You are an ADVANCED AUTONOMOUS CLI CODING AGENT powered by Gemini AI.

🎯 CORE CAPABILITIES:
- Full-stack development (Python, JavaScript, TypeScript, React, Node.js, etc.)
- Advanced file operations and project management
- Terminal command execution with PowerShell support
- Code analysis, generation, and refactoring
- Error detection and autonomous fixing
- Web information retrieval
- Multi-threaded task execution
- Context-aware decision making

TOOLS:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Begin!

Question: {input}
Thought: {agent_scratchpad}"""

        prompt = PromptTemplate(
            input_variables=["input", "agent_scratchpad", "tools", "tool_names"],
            template=prompt_template
        )

        # Create agent
        agent = create_react_agent(llm, tools, prompt)
        agent_executor = AgentExecutor(
            agent=agent,
            tools=tools,
            memory=self.memory,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=10
        )

        while True:
            try:
                user_input = input("\n🤖 agent> ").strip()

                if user_input.lower() in ['exit', 'quit', 'bye']:
                    print("👋 Goodbye! Happy coding!")
                    break

                if user_input.lower() == 'help':
                    self.show_help()
                    continue

                if user_input.lower() == 'status':
                    self.show_status()
                    continue

                if not user_input:
                    continue

                # Add context to user input
                context_info = f"""
Current Context:
- Directory: {self.context.current_directory}
- Active Files: {", ".join(self.context.active_files[-5:]) if self.context.active_files else "None"}
- Last Command: {self.context.command_history[-1] if self.context.command_history else "None"}

User Request: {user_input}"""

                # Execute with context
                print(f"\n🧠 Processing: {user_input}")
                print("-" * 50)

                # Run agent with error handling
                try:
                    result = agent_executor.invoke({
                        "input": context_info
                    })

                    if result.get("output"):
                        print(f"\n✅ Task completed: {result['output']}")

                except Exception as e:
                    print(f"❌ Agent error: {str(e)}")
                    # Try to fix the error and continue
                    fix_suggestion = self.fix_errors(str(e))
                    print(f"🔧 Suggested fix: {fix_suggestion}")

            except KeyboardInterrupt:
                print("\n⏸️ Interrupted. Type 'exit' to quit or continue with new command.")
                continue
            except Exception as e:
                print(f"❌ Unexpected error: {str(e)}")
                continue

    def show_help(self):
        """Show help information"""
        help_text = """
🤖 ADVANCED CLI CODING AGENT - HELP

🎯 CAPABILITIES:
• Build complete applications (React, Node.js, Python, etc.)
• Analyze and fix code errors automatically
• Generate code from natural language descriptions
• Refactor and optimize existing code
• Search and manage project files
• Execute terminal commands safely
• Retrieve web information for development
• Run tests and validate code

💡 EXAMPLE COMMANDS:
• "Create a React TypeScript app with routing"
• "Build a Python FastAPI server with authentication"
• "Fix the syntax errors in my code"
• "Analyze the performance of this function"
• "Generate unit tests for my module"
• "Refactor this code to be more modular"
• "Search for all TODO comments in my project"
• "Install dependencies and run the development server"

🔧 SPECIAL COMMANDS:
• help - Show this help
• status - Show current context and active files
• exit/quit - Exit the agent

🚀 AUTONOMOUS FEATURES:
• Auto-detects project type and requirements
• Suggests improvements and optimizations
• Handles errors and provides fixes
• Maintains context across conversations
• Learns from your coding patterns
"""
        print(help_text)

    def show_status(self):
        """Show current agent status"""
        print(f"""
📊 AGENT STATUS:
• Current Directory: {self.context.current_directory}
• Active Files: {len(self.context.active_files)}
• Command History: {len(self.context.command_history)} commands
• Last Error: {self.context.last_error or 'None'}
• Memory: {len(self.memory.buffer)} messages
• Cache Size: {len(self.cache)} items

📁 Recent Files:
{chr(10).join([f"  • {f}" for f in self.context.active_files[-5:]]) if self.context.active_files else "  None"}

⚡ Recent Commands:
{chr(10).join([f"  • {cmd}" for cmd in self.context.command_history[-3:]]) if self.context.command_history else "  None"}
""")

# Initialize and run the agent
if __name__ == "__main__":
    agent = AdvancedCodingAgent()
    agent.run_agent()